extends TextureButton

# ----- Signals -----
signal hovered(title: String, description: String)
signal unhovered

# ----- Exported Variables -----
@export_file("*.tscn") var target_scene: String = "res://maps/doi_tre/scenes/doi_tre.tscn"
@export var button_title: String = "Gò Mun"
@export var button_description: String = "Khu vực khảo cổ Gò Mun với nhiều hiện vật quý giá."
@export var is_locked: bool = false  # Mặc định là không khóa

# ----- Lifecycle -----
func _ready():
	if not is_instance_valid(self):
		push_error("TextureButton instance is invalid")
		return

	# Connect signals with error handling
	mouse_entered.connect(_on_mouse_entered)
	mouse_exited.connect(_on_mouse_exited)
	pressed.connect(_on_pressed)

# ----- Event Handlers -----
func _on_mouse_entered():
	if not is_instance_valid(self):
		return

	if is_locked:
		# Nế<PERSON> bị khóa, emit signal với thông báo khóa
		hovered.emit(button_title, "<PERSON>hu vực này hiện đang bị khóa. <PERSON><PERSON><PERSON> hoàn thành nhiệm vụ trước để mở khóa.")
		return

	# Emit hover signal với thông tin bình thường
	hovered.emit(button_title, button_description)

func _on_mouse_exited():
	if not is_instance_valid(self):
		return

	unhovered.emit()

func _on_pressed():
	if is_locked:
		# Có thể thêm hiệu ứng âm thanh hoặc visual để chỉ ra rằng button bị khóa
		print("This area is locked!")
		return

	if not FileAccess.file_exists(target_scene):
		push_error("Cannot load target scene: " + target_scene)
		return

	# Chuyển scene với delay nhỏ
	await get_tree().create_timer(0.1).timeout
	SceneManager.goto_scene(target_scene)

# ----- Public Methods -----
func unlock() -> void:
	is_locked = false
	print("Area unlocked!")

func lock() -> void:
	is_locked = true
	print("Area locked!")