extends Node2D

# ----- Onready Variables -----
@onready var info_box = $InfoBox
@onready var title = $InfoBox/Title
@onready var description = $InfoBox/Description
@onready var image = $InfoBox/Image
@onready var confirmation_dialog = $ConfirmationDialog
@onready var play_button = $ConfirmationDialog/PlayButton
@onready var back_foot_btn = $ConfirmationDialog/BackFootBTN
@onready var tang1_button = $Tower/Tang1Button
@onready var tang2_button = $Tower/Tang2Button

# ----- Variables -----
var current_target_scene: String = ""
var floor_buttons: Array
var is_floors_locked: bool = false
var is_floor_clicked: bool = false  # Biến để theo dõi xem người dùng đã nhấn vào tầng nào chưa

# Animation tweens for InfoBox
var show_tween: Tween
var hide_tween: Tween

# ----- Lifecycle Methods -----
func _ready():
    # Lưu tất cả các nút tầng vào mảng để dễ quản lý
    floor_buttons = [$Tower/Tang1Button, $Tower/Tang2Button]

    # Kết nối signals cho các tầng trong Tower
    var tower_node = get_node_or_null("Tower")
    if tower_node and is_instance_valid(tower_node):
        for floor_node in tower_node.get_children():
            if floor_node.has_signal("hovered"):
                floor_node.connect("hovered", Callable(self, "_on_floor_hovered"))
            if floor_node.has_signal("clicked"):
                floor_node.connect("clicked", Callable(self, "_on_floor_clicked"))
    else:
        print("WARNING: Tower node not found or invalid")

    # Kết nối signals cho các nút tầng
    tang1_button.pressed.connect(_on_tang1_pressed)
    tang2_button.pressed.connect(_on_tang2_pressed)

    # Kết nối signals cho các nút trong dialog
    play_button.pressed.connect(_on_play_pressed)
    back_foot_btn.pressed.connect(_on_back_pressed)

    # Ẩn dialog khi khởi động
    confirmation_dialog.visible = false

    # Cấu hình InfoBox
    info_box.custom_minimum_size = Vector2(300, 500)
    # Đảm bảo InfoBox ẩn khi khởi động, nhưng sẽ hiển thị khi nhấn vào tầng
    info_box.visible = false
    # Đặt initial state cho animation
    info_box.modulate = Color(1, 1, 1, 0)
    info_box.scale = Vector2(0.8, 0.8)

    # Cấu hình Description
    description.bbcode_enabled = true
    description.fit_content = true
    description.scroll_active = false

# ----- Helper Functions -----
func show_info_box_animated():
    """Hiển thị InfoBox với animation mượt mà"""
    if hide_tween:
        hide_tween.kill()

    info_box.visible = true
    show_tween = create_tween()
    show_tween.set_parallel(true)
    show_tween.tween_property(info_box, "modulate", Color(1, 1, 1, 1), 0.3).set_ease(Tween.EASE_OUT)
    show_tween.tween_property(info_box, "scale", Vector2(1, 1), 0.3).set_ease(Tween.EASE_OUT).set_trans(Tween.TRANS_BACK)

func hide_info_box_animated():
    """Ẩn InfoBox với animation mượt mà"""
    if show_tween:
        show_tween.kill()

    hide_tween = create_tween()
    hide_tween.set_parallel(true)
    hide_tween.tween_property(info_box, "modulate", Color(1, 1, 1, 0), 0.2).set_ease(Tween.EASE_IN)
    hide_tween.tween_property(info_box, "scale", Vector2(0.8, 0.8), 0.2).set_ease(Tween.EASE_IN)

    # Ẩn hoàn toàn sau khi animation kết thúc
    hide_tween.tween_callback(func(): info_box.visible = false).set_delay(0.2)

func lock_other_floors(except_button: TextureButton) -> void:
    is_floors_locked = true
    for button in floor_buttons:
        if button != except_button:
            button.set_process_input(false)
            button.modulate = Color(0.5, 0.5, 0.5, 1.0)  # Làm mờ các nút không được chọn
    # Không ẩn InfoBox khi khóa để người dùng vẫn thấy thông tin tầng đã chọn
    # info_box.visible = false

func unlock_all_floors() -> void:
    is_floors_locked = false
    is_floor_clicked = false  # Đặt lại trạng thái khi người dùng nhấn "Quay lại"

    for button in floor_buttons:
        button.set_process_input(true)
        button.modulate = Color(1, 1, 1, 1)  # Khôi phục màu sắc bình thường

    # Ẩn InfoBox khi người dùng nhấn "Quay lại"
    hide_info_box_animated()

# ----- Signal Handlers -----
func _on_floor_hovered(title_text, desc_text, img):
    # Nếu các tầng đang bị khóa, không hiển thị InfoBox khi hover
    if is_floors_locked:
        return

    # Nếu đã có tầng được nhấn, không thay đổi trạng thái hiển thị của InfoBox
    if is_floor_clicked:
        return

    # Nếu di chuột ra khỏi tầng (title_text rỗng) và chưa có tầng nào được nhấn
    if title_text == "":
        hide_info_box_animated()
        return

    # Cập nhật nội dung InfoBox
    title.text = title_text
    description.clear()
    description.append_text("[center][color=#E6E6E6]" + desc_text + "[/color][/center]")
    if img:
        image.texture = img
    show_info_box_animated()

func _on_floor_clicked(title_text, desc_text, img):
    # Hiển thị thông tin tầng khi được nhấn
    if is_floors_locked:
        return

    if title_text == "":
        return

    # Đánh dấu là đã có tầng được nhấn
    is_floor_clicked = true

    # Đảm bảo InfoBox hiển thị đúng
    title.text = title_text
    description.clear()
    description.append_text("[center][color=#E6E6E6]" + desc_text + "[/color][/center]")
    if img:
        image.texture = img

    # Đảm bảo InfoBox hiển thị và ở trên cùng
    show_info_box_animated()
    info_box.z_index = 10  # Đặt z_index cao để hiển thị trên các phần tử khác

    # In thông báo để debug
    print("InfoBox displayed with title: ", title_text)

func _on_tang1_pressed():
    # Đảm bảo InfoBox vẫn hiển thị khi nhấn vào tầng
    if tang1_button.has_signal("clicked"):
        tang1_button.emit_signal("clicked", tang1_button.FLOOR_TITLE, tang1_button.FLOOR_DESCRIPTION, tang1_button.FLOOR_IMAGE)

    current_target_scene = "res://maps/New_loor/scenes/MapScene.tscn"
    confirmation_dialog.visible = true
    lock_other_floors(tang1_button)

func _on_tang2_pressed():
    # Đảm bảo InfoBox vẫn hiển thị khi nhấn vào tầng
    if tang2_button.has_signal("clicked"):
        tang2_button.emit_signal("clicked", tang2_button.FLOOR_TITLE, tang2_button.FLOOR_DESCRIPTION, tang2_button.FLOOR_IMAGE)

    current_target_scene = "res://maps/New_loor/scenes/MapScene.tscn"
    confirmation_dialog.visible = true
    lock_other_floors(tang2_button)

func _on_play_pressed():
    if current_target_scene.is_empty():
        push_error("No target scene selected")
        return

    # Chuyển đến scene được chỉ định
    if not current_target_scene.is_empty():
        # Ẩn dialog xác nhận
        confirmation_dialog.visible = false
        # Chuyển đến scene mới
        SceneManager.goto_scene(current_target_scene)
    else:
        # Nếu không có scene nào được chỉ định, chỉ ẩn dialog
        confirmation_dialog.visible = false
        unlock_all_floors()

func _on_back_pressed():
    confirmation_dialog.visible = false
    unlock_all_floors()  # Mở khóa lại tất cả các nút khi nhấn back



