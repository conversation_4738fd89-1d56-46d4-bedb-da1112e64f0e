extends Node

# MapScene Validator - Tool to validate MapScene functionality
# This script can be attached to a Node in the scene to run validation tests

func _ready():
	print("🔍 MapScene Validator starting...")
	call_deferred("run_validation")

func run_validation():
	print("\n" + "="*50)
	print("🧪 MAPSCENE VALIDATION REPORT")
	print("="*50)
	
	validate_scene_structure()
	validate_button_scripts()
	validate_target_scenes()
	validate_assets()
	
	print("\n✅ MapScene validation completed!")
	print("="*50)

func validate_scene_structure():
	print("\n📋 1. SCENE STRUCTURE VALIDATION")
	print("-" * 30)
	
	var scene_path = "res://maps/New_loor/scenes/MapScene.tscn"
	if not ResourceLoader.exists(scene_path):
		print("❌ MapScene.tscn not found!")
		return
	
	print("✅ MapScene.tscn exists")
	
	# Check if scene can be loaded
	var scene_resource = load(scene_path)
	if not scene_resource:
		print("❌ Cannot load MapScene.tscn")
		return
	
	print("✅ MapScene.tscn can be loaded")

func validate_button_scripts():
	print("\n🔘 2. BUTTON SCRIPTS VALIDATION")
	print("-" * 30)
	
	var button_scripts = [
		"res://maps/New_loor/scripts/DaoThinhButton.gd",
		"res://maps/New_loor/scripts/phung_nguyen_button.gd",
		"res://maps/New_loor/scripts/phongchaubutton.gd",
		"res://maps/New_loor/scripts/dongdau_button.gd",
		"res://maps/New_loor/scripts/gomun_button.gd",
		"res://maps/New_loor/scripts/ngoclu_button.gd"
	]
	
	for script_path in button_scripts:
		if ResourceLoader.exists(script_path):
			print("✅ " + script_path.get_file())
		else:
			print("❌ " + script_path.get_file() + " not found!")

func validate_target_scenes():
	print("\n🎯 3. TARGET SCENES VALIDATION")
	print("-" * 30)
	
	var target_scenes = {
		"DaoThinhButton": "res://maps/hang_an/scenes/hang_an.tscn",
		"PhungNguyenButton": "res://maps/rung_nuong/scenes/rung_nuong.tscn",
		"PhongChauButton": "res://maps/lang_van_lang/scenes/lang_van_lang.tscn",
		"DongDauButton": "res://maps/dong_dau/scenes/dong_dau.tscn",
		"GoMunButton": "res://maps/doi_tre/scenes/doi_tre.tscn",
		"NgocLuButton": "res://maps/suoi_thieng/scenes/suoi_thieng.tscn"
	}
	
	for button_name in target_scenes:
		var scene_path = target_scenes[button_name]
		if ResourceLoader.exists(scene_path):
			print("✅ " + button_name + " -> " + scene_path.get_file())
		else:
			print("❌ " + button_name + " -> " + scene_path + " NOT FOUND!")

func validate_assets():
	print("\n🖼️ 4. ASSETS VALIDATION")
	print("-" * 30)
	
	var assets = [
		"res://assets/images/background/screens/Phùng Nguyên.png",
		"res://assets/images/item/green_button.png",
		"res://assets/images/item/red_button.png"
	]
	
	for asset_path in assets:
		if ResourceLoader.exists(asset_path):
			print("✅ " + asset_path.get_file())
		else:
			print("❌ " + asset_path + " NOT FOUND!")

# Function to test button functionality (call manually if needed)
func test_button_functionality():
	print("\n🧪 TESTING BUTTON FUNCTIONALITY")
	print("-" * 30)
	
	# This would need to be called from within the MapScene
	# to access the actual button instances
	print("⚠️ Button functionality test requires running from MapScene context")
