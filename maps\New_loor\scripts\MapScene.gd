extends Node2D

@onready var info_box = $InfoBox
@onready var map_buttons = [
	$DaoThinhButton,
	$PhungNguyenButton,
	$PhongChauButton,
	$GoMunButton,
	$DongDauButton,
	$NgocLuButton
]

# Texture resources with validation
var RED_BUTTON: Texture2D
var GREEN_BUTTON: Texture2D

func _ready():
	# Load and validate textures
	_load_textures()

	# Validate info_box
	if not info_box:
		push_error("InfoBox not found in MapScene")
		return

	# Setup buttons
	_setup_buttons()

	# Validate all target scenes
	_validate_target_scenes()

func _load_textures():
	"""Load and validate button textures"""
	var red_path = "res://assets/images/item/red_button.png"
	var green_path = "res://assets/images/item/green_button.png"

	if ResourceLoader.exists(red_path):
		RED_BUTTON = load(red_path)
	else:
		push_error("Red button texture not found: " + red_path)

	if ResourceLoader.exists(green_path):
		GREEN_BUTTON = load(green_path)
	else:
		push_error("Green button texture not found: " + green_path)

func _setup_buttons():
	"""Setup all map buttons with proper validation"""
	for btn in map_buttons:
		if not btn:
			push_warning("Button is null in map_buttons array")
			continue

		if not is_instance_valid(btn):
			push_warning("Button instance is invalid: " + str(btn))
			continue

		# Connect signals with error handling (use Callable in Godot 4)
		var hovered_cb = Callable(self, "_on_button_hovered")
		var unhovered_cb = Callable(self, "_on_button_unhovered")
		if not btn.is_connected("hovered", hovered_cb):
			var result = btn.connect("hovered", hovered_cb)
			if result != OK:
				push_error("Failed to connect hovered signal for button: " + btn.name)
		if not btn.is_connected("unhovered", unhovered_cb):
			var result2 = btn.connect("unhovered", unhovered_cb)
			if result2 != OK:
				push_error("Failed to connect unhovered signal for button: " + btn.name)

		# Validate and set lock status based on texture
		_validate_button_lock_status(btn)

func _validate_button_lock_status(btn: TextureButton):
	"""Validate and set button lock status based on texture"""
	if not btn or not is_instance_valid(btn):
		return

	# Check if button has is_locked property
	if not btn.has_method("set") or not "is_locked" in btn:
		push_warning("Button " + btn.name + " doesn't have is_locked property")
		return

	# Set lock status based on texture
	if btn.texture_normal == GREEN_BUTTON and GREEN_BUTTON:
		btn.is_locked = false
		print("Button ", btn.name, " set to unlocked (green texture)")
	elif btn.texture_normal == RED_BUTTON and RED_BUTTON:
		btn.is_locked = true
		print("Button ", btn.name, " set to locked (red texture)")
	else:
		# Default to unlocked if texture doesn't match or textures failed to load
		btn.is_locked = false
		print("Button ", btn.name, " defaulted to unlocked (unknown texture or texture load failed)")

func _validate_target_scenes():
	"""Validate that all button target scenes exist"""
	print("🔍 Validating target scenes for all buttons...")

	for btn in map_buttons:
		if not btn or not is_instance_valid(btn):
			continue

		# Check if button has target_scene property
		if not "target_scene" in btn:
			push_warning("Button " + btn.name + " doesn't have target_scene property")
			continue

		var target_scene = btn.target_scene
		if target_scene.is_empty():
			push_error("Button " + btn.name + " has empty target_scene")
			continue

		if not ResourceLoader.exists(target_scene):
			push_error("Button " + btn.name + " target scene doesn't exist: " + target_scene)
			# Lock the button if scene doesn't exist
			if "is_locked" in btn:
				btn.is_locked = true
				print("🔒 Locked button " + btn.name + " due to missing target scene")
		else:
			print("✅ Button " + btn.name + " target scene validated: " + target_scene)

func _on_button_hovered(title: String, description: String):
	if info_box and is_instance_valid(info_box):
		info_box.show_info(title, description)
	else:
		push_error("InfoBox is not available for showing hover info")

func _on_button_unhovered():
	if info_box and is_instance_valid(info_box):
		info_box.hide_info()
	else:
		push_error("InfoBox is not available for hiding hover info")

# Hàm để mở khóa button (gọi khi hoàn thành điều kiện nào đó)
func unlock_button(button_name: String) -> void:
	var button = get_node_or_null(button_name)
	if not button or not is_instance_valid(button):
		push_error("Cannot find button to unlock: " + button_name)
		return

	if not GREEN_BUTTON:
		push_error("Green button texture not loaded, cannot unlock button")
		return

	button.texture_normal = GREEN_BUTTON
	if "is_locked" in button:
		button.is_locked = false
		print("Unlocked button: ", button_name)
	else:
		push_warning("Button " + button_name + " doesn't have is_locked property")

# Hàm để khóa button
func lock_button(button_name: String) -> void:
	var button = get_node_or_null(button_name)
	if not button or not is_instance_valid(button):
		push_error("Cannot find button to lock: " + button_name)
		return

	if not RED_BUTTON:
		push_error("Red button texture not loaded, cannot lock button")
		return

	button.texture_normal = RED_BUTTON
	if "is_locked" in button:
		button.is_locked = true
		print("Locked button: ", button_name)
	else:
		push_warning("Button " + button_name + " doesn't have is_locked property")

# Hàm để lấy trạng thái khóa của button
func is_button_locked(button_name: String) -> bool:
	var button = get_node_or_null(button_name)
	if not button or not is_instance_valid(button):
		push_error("Cannot find button: " + button_name)
		return true  # Default to locked if button not found

	if "is_locked" in button:
		return button.is_locked
	else:
		push_warning("Button " + button_name + " doesn't have is_locked property")
		return false

# Hàm debug để in trạng thái tất cả buttons
func debug_print_button_states():
	print("🔍 DEBUG: Current button states:")
	for btn in map_buttons:
		if btn and is_instance_valid(btn):
			var locked_status = "UNKNOWN"
			if "is_locked" in btn:
				locked_status = "LOCKED" if btn.is_locked else "UNLOCKED"
			print("  - " + btn.name + ": " + locked_status)
		else:
			print("  - Invalid button in array")
