extends Panel

@onready var title_label = $TitleLabel
@onready var description_label = $DescriptionLabel
@onready var title_background = $TitleBackground

const LOCKED_TEXT_COLOR = Color(1, 0.4, 0.4)     # Màu đỏ nhạt cho text khóa
const NORMAL_TEXT_COLOR = Color(0.9, 0.9, 0.9)   # Màu xám nhạt cho text bình thường
const LOCKED_TITLE_COLOR = Color(1, 0.6, 0.6)    # Màu đỏ nhạt cho title khóa
const NORMAL_TITLE_COLOR = Color(1, 0.9, 0.7)    # Màu vàng nhạt cho title bình thường

# Animation tweens
var show_tween: Tween
var hide_tween: Tween

func _ready():
	# Validate child nodes
	if not title_label:
		push_error("TitleLabel not found in InfoBox")
		return
	if not description_label:
		push_error("DescriptionLabel not found in InfoBox")
		return
	if not title_background:
		push_error("TitleBackground not found in InfoBox")
		return

	# Đặt initial state
	modulate = Color(1, 1, 1, 0)
	scale = Vector2(0.8, 0.8)

	print("✅ InfoBox initialized successfully")

func show_info(title: String, description: String):
	# Validate nodes before using them
	if not title_label or not is_instance_valid(title_label):
		push_error("TitleLabel is not available for showing info")
		return
	if not description_label or not is_instance_valid(description_label):
		push_error("DescriptionLabel is not available for showing info")
		return

	# Dừng animation hiện tại nếu có
	if hide_tween and hide_tween.is_valid():
		hide_tween.kill()

	# Set title with validation
	if title.is_empty():
		title_label.text = "Khu vực"
	else:
		title_label.text = title

	# Format description với BBCode để có styling tốt hơn
	var formatted_description = ""
	if description.contains("bị khóa"):
		formatted_description = "[color=#FF6666][b]🔒 " + description + "[/b][/color]"
		title_label.add_theme_color_override("font_color", LOCKED_TITLE_COLOR)
		description_label.add_theme_color_override("default_color", LOCKED_TEXT_COLOR)
	else:
		formatted_description = "[color=#E6E6E6]" + description + "[/color]"
		title_label.add_theme_color_override("font_color", NORMAL_TITLE_COLOR)
		description_label.add_theme_color_override("default_color", NORMAL_TEXT_COLOR)

	description_label.text = formatted_description

	# Hiển thị với animation mượt mà
	visible = true
	show_tween = create_tween()
	if show_tween:
		show_tween.set_parallel(true)
		show_tween.tween_property(self, "modulate", Color(1, 1, 1, 1), 0.3).set_ease(Tween.EASE_OUT)
		show_tween.tween_property(self, "scale", Vector2(1, 1), 0.3).set_ease(Tween.EASE_OUT).set_trans(Tween.TRANS_BACK)

func hide_info():
	# Dừng animation hiện tại nếu có
	if show_tween and show_tween.is_valid():
		show_tween.kill()

	# Ẩn với animation mượt mà
	hide_tween = create_tween()
	if hide_tween:
		hide_tween.set_parallel(true)
		hide_tween.tween_property(self, "modulate", Color(1, 1, 1, 0), 0.2).set_ease(Tween.EASE_IN)
		hide_tween.tween_property(self, "scale", Vector2(0.8, 0.8), 0.2).set_ease(Tween.EASE_IN)

		# Ẩn hoàn toàn sau khi animation kết thúc
		hide_tween.tween_callback(func(): visible = false).set_delay(0.2)
	else:
		# Fallback if tween creation fails
		visible = false
