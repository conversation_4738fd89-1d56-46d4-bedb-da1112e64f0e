[gd_scene load_steps=18 format=3 uid="uid://cu4q534chkbc2"]

[ext_resource type="Script" path="res://maps/New_loor/scripts/main_foot.gd" id="1_bwe87"]
[ext_resource type="Texture2D" uid="uid://r0fsb8failkd" path="res://assets/images/background/screens/<EMAIL>" id="2_kib6s"]
[ext_resource type="PackedScene" uid="uid://nhst2kv1iog" path="res://Home/scenes/backbutton.tscn" id="3_4kcrj"]
[ext_resource type="Script" path="res://maps/New_loor/scripts/tang1button.gd" id="4_slwep"]
[ext_resource type="Texture2D" uid="uid://bwdlaydxajpg8" path="res://assets/images/background/screens/Tang_Thap.png" id="5_06k64"]
[ext_resource type="Script" path="res://maps/New_loor/scripts/tang2button.gd" id="5_ermjn"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_5xosa"]
bg_color = Color(0.08, 0.08, 0.12, 0.96)
border_width_left = 4
border_width_top = 4
border_width_right = 4
border_width_bottom = 4
border_color = Color(0.7, 0.5, 0.2, 1)
corner_radius_top_left = 20
corner_radius_top_right = 20
corner_radius_bottom_right = 20
corner_radius_bottom_left = 20
shadow_size = 10
shadow_offset = Vector2(5, 5)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_title_header"]
bg_color = Color(0.7, 0.5, 0.2, 0.4)
corner_radius_top_left = 15
corner_radius_top_right = 15
corner_radius_bottom_right = 15
corner_radius_bottom_left = 15

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_image_frame"]
bg_color = Color(0.15, 0.15, 0.2, 0.8)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.6, 0.4, 0.15, 1)
corner_radius_top_left = 12
corner_radius_top_right = 12
corner_radius_bottom_right = 12
corner_radius_bottom_left = 12

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_vh41q"]
bg_color = Color(0.6, 0.6, 0.6, 0)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_oy65n"]
bg_color = Color(0.6, 0.6, 0.6, 0)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_g4d52"]
bg_color = Color(0.6, 0.6, 0.6, 0)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_6x63k"]
bg_color = Color(0.6, 0.6, 0.6, 0)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_stt8x"]
bg_color = Color(0.6, 0.6, 0.6, 0)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_dqj73"]
bg_color = Color(0.6, 0.6, 0.6, 0)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_wrsjq"]
bg_color = Color(0.6, 0.6, 0.6, 0)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_eyg6y"]
bg_color = Color(0.6, 0.6, 0.6, 0)

[node name="Mainfoot" type="Node2D"]
position = Vector2(0, -1)
script = ExtResource("1_bwe87")

[node name="TextureRect" type="TextureRect" parent="."]
offset_right = 1152.0
offset_bottom = 645.0
texture = ExtResource("2_kib6s")
metadata/_edit_use_anchors_ = true

[node name="BackButton" parent="." instance=ExtResource("3_4kcrj")]
offset_left = 29.0
offset_top = 281.0
offset_right = 69.0
offset_bottom = 321.0
metadata/_edit_use_anchors_ = true

[node name="Tower" type="Node2D" parent="."]

[node name="Tang1Button" type="TextureButton" parent="Tower"]
offset_left = 571.0
offset_top = 559.0
offset_right = 720.0
offset_bottom = 614.0
script = ExtResource("4_slwep")

[node name="Tang2Button" type="TextureButton" parent="Tower"]
offset_left = 573.0
offset_top = 468.0
offset_right = 718.0
offset_bottom = 525.0
script = ExtResource("5_ermjn")

[node name="InfoBox" type="Panel" parent="."]
visible = false
offset_left = 814.0
offset_top = 161.0
offset_right = 1245.0
offset_bottom = 686.0
theme_override_styles/panel = SubResource("StyleBoxFlat_5xosa")

[node name="TitleBackground" type="Panel" parent="InfoBox"]
layout_mode = 0
offset_left = 15.0
offset_top = 15.0
offset_right = 416.0
offset_bottom = 75.0
theme_override_styles/panel = SubResource("StyleBoxFlat_title_header")

[node name="Title" type="Label" parent="InfoBox"]
layout_mode = 0
offset_left = 25.0
offset_top = 25.0
offset_right = 406.0
offset_bottom = 65.0
theme_override_colors/font_color = Color(1, 0.9, 0.7, 1)
theme_override_font_sizes/font_size = 26
text = "Tầng"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Description" type="RichTextLabel" parent="InfoBox"]
layout_mode = 0
offset_left = 20.0
offset_top = 85.0
offset_right = 411.0
offset_bottom = 305.0
theme_override_colors/default_color = Color(0.9, 0.9, 0.9, 1)
theme_override_colors/font_selected_color = Color(1, 1, 1, 1)
theme_override_font_sizes/normal_font_size = 18
bbcode_enabled = true
fit_content = true
scroll_active = false
autowrap_mode = 2

[node name="ImageFrame" type="Panel" parent="InfoBox"]
layout_mode = 0
offset_left = 25.0
offset_top = 320.0
offset_right = 406.0
offset_bottom = 505.0
theme_override_styles/panel = SubResource("StyleBoxFlat_image_frame")

[node name="Image" type="TextureRect" parent="InfoBox"]
layout_mode = 0
offset_left = 35.0
offset_top = 330.0
offset_right = 396.0
offset_bottom = 495.0
texture = ExtResource("5_06k64")
expand_mode = 1
stretch_mode = 5

[node name="Label" type="Label" parent="."]
offset_left = 92.0
offset_top = 378.0
offset_right = 314.0
offset_bottom = 441.0
theme_override_font_sizes/font_size = 45
text = "Chọn tầng
"

[node name="ConfirmationDialog" type="Control" parent="."]
layout_mode = 3
anchors_preset = 0
offset_right = 40.0
offset_bottom = 40.0

[node name="PlayButton" type="Button" parent="ConfirmationDialog"]
layout_mode = 0
offset_left = 83.0
offset_top = 475.0
offset_right = 321.0
offset_bottom = 546.0
theme_override_font_sizes/font_size = 45
theme_override_styles/focus = SubResource("StyleBoxFlat_vh41q")
theme_override_styles/hover = SubResource("StyleBoxFlat_oy65n")
theme_override_styles/pressed = SubResource("StyleBoxFlat_g4d52")
theme_override_styles/normal = SubResource("StyleBoxFlat_6x63k")
text = "Bắt Đầu"

[node name="BackFootBTN" type="Button" parent="ConfirmationDialog"]
layout_mode = 0
offset_left = 145.0
offset_top = 562.0
offset_right = 251.0
offset_bottom = 605.0
theme_override_font_sizes/font_size = 25
theme_override_styles/focus = SubResource("StyleBoxFlat_stt8x")
theme_override_styles/hover = SubResource("StyleBoxFlat_dqj73")
theme_override_styles/pressed = SubResource("StyleBoxFlat_wrsjq")
theme_override_styles/normal = SubResource("StyleBoxFlat_eyg6y")
text = "Quay lại"
